from typing import Optional

from apps.connectors.integrations.actions.threat_intelligence import (
    GetThreatDetails,
    ThreatDetailsResult,
)
from apps.connectors.integrations.schemas import EmailThreatIdentifierArgs
import apps.connectors.integrations.schemas.ocsf as ocsf
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.api import (
    ProofpointV1Api,
)


def map_threat_type_to_osint_type(threat_type: str) -> ocsf.OSINTIndicatorType:
    """
    Map Proofpoint threat types to OCSF OSINT indicator types.

    Args:
        threat_type: Proofpoint threat type (attachment, url, message text, etc.)

    Returns:
        Corresponding OCSF OSINTIndicatorType enum value
    """
    threat_type_lower = threat_type.lower() if threat_type else ""

    mapping = {
        "attachment": ocsf.OSINTIndicatorType.FILE,
        "url": ocsf.OSINTIndicatorType.URL,
        "message text": ocsf.OSINTIndicatorType.EMAIL,
        "hash": ocsf.OSINTIndicatorType.HASH,
        "file": ocsf.OSINTIndicatorType.FILE,
    }

    return mapping.get(threat_type_lower, ocsf.OSINTIndicatorType.OTHER)


def map_threat_status_to_confidence(threat_status: str) -> ocsf.Confidence:
    """
    Map Proofpoint threat status to OCSF confidence levels.

    Args:
        threat_status: Proofpoint threat status (active, cleared)

    Returns:
        Corresponding OCSF Confidence enum value
    """
    status_lower = threat_status.lower() if threat_status else ""

    mapping = {
        "active": ocsf.Confidence.HIGH,
        "cleared": ocsf.Confidence.MEDIUM,
    }

    return mapping.get(status_lower, ocsf.Confidence.UNKNOWN)


def normalize_threat_actors(actors_data: list) -> Optional[str]:
    """
    Normalize Proofpoint actor data to a comment string.

    Since OSINT doesn't have a specific threat_actor field, we'll include
    actor information in the comment field for analyst reference.

    Args:
        actors_data: List of actor objects from Proofpoint API with 'id' and 'name' fields

    Returns:
        Formatted actor information string or None if no valid actor data
    """
    if not actors_data or not isinstance(actors_data, list):
        return None

    actor_details = []
    for actor in actors_data:
        if isinstance(actor, dict) and actor.get("name"):
            name = actor.get("name")
            actor_id = actor.get("id")
            if actor_id:
                actor_details.append(f"{name} (ID: {actor_id})")
            else:
                actor_details.append(name)

    if actor_details:
        return f"Associated threat actors: {', '.join(actor_details)}"

    return None


def normalize_threat_intelligence_arrays(arrays_data: list, label: str) -> Optional[str]:
    """
    Normalize threat intelligence arrays (families, malware, techniques, brands) to comment string.

    Args:
        arrays_data: List of objects with 'id' and 'name' fields
        label: Label for the array type (e.g., "Families", "Malware")

    Returns:
        Formatted string or None if no valid data
    """
    if not arrays_data or not isinstance(arrays_data, list):
        return None

    names = []
    for item in arrays_data:
        if isinstance(item, dict) and item.get("name"):
            names.append(item.get("name"))

    if names:
        return f"{label}: {', '.join(names)}"


def normalize_threat_to_osint(threat_data: dict) -> ocsf.OSINT:
    """
    Convert Proofpoint threat summary data to OCSF OSINT format.

    Args:
        threat_data: Raw threat data from Proofpoint Threat API

    Returns:
        OSINT object with normalized threat intelligence data
    """
    # Use correct field names from official Threat API documentation
    threat_id = threat_data.get("id")
    threat_type = threat_data.get("type", "")
    classification = threat_data.get("category", "")
    threat_status = threat_data.get("status", "")
    threat_name = threat_data.get("name")
    identified_at = threat_data.get("identifiedAt")
    severity = threat_data.get("severity")
    actors = threat_data.get("actors", [])
    families = threat_data.get("families", [])
    malware = threat_data.get("malware", [])
    techniques = threat_data.get("techniques", [])
    brands = threat_data.get("brands", [])

    # Use threat name as the primary value, fallback to threat_id
    threat_value = threat_name or threat_id or ""

    # Map Proofpoint fields to OCSF OSINT
    osint_type = map_threat_type_to_osint_type(threat_type)
    confidence = map_threat_status_to_confidence(threat_status)

    # Normalize threat intelligence data to comments
    actor_comment = normalize_threat_actors(actors)
    families_comment = normalize_threat_intelligence_arrays(families, "Threat Families")
    malware_comment = normalize_threat_intelligence_arrays(malware, "Malware")
    techniques_comment = normalize_threat_intelligence_arrays(techniques, "Techniques")
    brands_comment = normalize_threat_intelligence_arrays(brands, "Brands")

    # Build comprehensive comment with threat details
    comment_parts = []
    if classification:
        comment_parts.append(f"Category: {classification}")
    if threat_status:
        comment_parts.append(f"Status: {threat_status}")
    if threat_type:
        comment_parts.append(f"Type: {threat_type}")
    if severity is not None:
        comment_parts.append(f"Severity: {severity}")
    if identified_at:
        comment_parts.append(f"Identified: {identified_at}")
    if actor_comment:
        comment_parts.append(actor_comment)
    if families_comment:
        comment_parts.append(families_comment)
    if malware_comment:
        comment_parts.append(malware_comment)
    if techniques_comment:
        comment_parts.append(techniques_comment)
    if brands_comment:
        comment_parts.append(brands_comment)

    comment = "; ".join(comment_parts) if comment_parts else None

    return ocsf.OSINT(
        uid=threat_id,
        value=threat_value,
        type_id=osint_type.id,
        category=classification or None,
        confidence_id=confidence.id,
        src_url=None, 
        vendor_name="Proofpoint",
        campaign=None,
        comment=comment,
    )


class ProofpointV1GetThreatDetails(GetThreatDetails):
    """
    Proofpoint implementation for retrieving detailed threat intelligence.

    This action fetches comprehensive threat details from Proofpoint's
    Threat API and normalizes the data into OCSF OSINT format
    for standardized threat intelligence consumption.

    Features:
    - Retrieves threat details via /v2/threat/summary/{threat_id}
    - Maps Proofpoint threat types (attachment, url, message text) to OCSF indicator types
    - Converts threat status (active, cleared) to confidence levels
    - Includes actor, family, malware, technique, and brand information
    - Provides severity scores and identification timestamps
    - Provides standardized OSINT output for SOC workflows
    """

    def execute(self, args: EmailThreatIdentifierArgs) -> ThreatDetailsResult:
        """
        Execute threat details retrieval for a given threat ID.

        Args:
            args: Arguments containing the email threat identifier

        Returns:
            ThreatDetailsResult containing normalized OSINT data

        Raises:
            IntegrationError: If API call fails or threat not found
        """
        api: ProofpointV1Api = self.integration.get_api()
        threat_id = args.email_threat_id.value

        # Fetch threat details from Proofpoint API
        threat_response = api.get_email_threat(threat_id)

        # Normalize the response to OCSF OSINT format
        osint_data = normalize_threat_to_osint(threat_response)

        return ThreatDetailsResult(result=osint_data)

    def get_permission_checks(self): # pragma: no cover
        """Return permission checks required for this action."""
        return []  # No specific permission checks required
