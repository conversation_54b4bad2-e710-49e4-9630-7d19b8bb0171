from apps.connectors.integrations import Integration

from .actions.add_alert_comment import NozomiVantageV1AddAlertComment
from .actions.event_sync import NozomiVantageV1EventSync
from .actions.update_lifecycle_status import NozomiVantageV1UpdateLifecycleStatus
from .api import <PERSON>zomiVantageV1<PERSON><PERSON>, NozomiVantageV1ApiError
from .health_check import ConnectionHealthCheck


class NozomiVantageV1Integration(Integration):
    api_class = NozomiVantageV1Api
    exception_types = (NozomiVantageV1ApiError,)
    actions = (
        NozomiVantageV1EventSync,
        NozomiVantageV1AddAlertComment,
        NozomiVantageV1UpdateLifecycleStatus,
    )
    critical_health_checks = (ConnectionHealthCheck,)
