from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
    UpdateLifecycleStatus,
    UpdateLifecycleStatusArgs,
    UpdateLifecycleStatusResult,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api import (
    NozomiVantageV1Api,
)


def map_corr_incident_status(status: CorrIncidentStatus) -> bool:
    """
    Map CorrIncidentStatus to Nozomi acknowledge status.

    Args:
        status: The CORR incident status

    Returns:
        bool: True to acknowledge the alert, False to unacknowledge
    """
    return {
        CorrIncidentStatus.NEW: False,  # Unacknowledge for new alerts
        CorrIncidentStatus.ASSIGNED: True,  # Acknowledge when assigned
        CorrIncidentStatus.REVIEWING: True,  # Acknowledge when reviewing
        CorrIncidentStatus.MITIGATED: True,  # Acknowledge when mitigated
        CorrIncidentStatus.CLOSED: True,  # Acknowledge when closed
    }[status]


class NozomiVantageV1UpdateLifecycleStatus(UpdateLifecycleStatus):
    def execute(
        self, args: UpdateLifecycleStatusArgs, **kwargs
    ) -> UpdateLifecycleStatusResult:
        api: NozomiVantageV1Api = self.integration.get_api()

        acknowledge = map_corr_incident_status(args.status)
        api.acknowledge_alerts([args.vendor_sync_id], acknowledge=acknowledge)

        return UpdateLifecycleStatusResult()

    def get_permission_checks(self, *args, **kwargs):
        return []
