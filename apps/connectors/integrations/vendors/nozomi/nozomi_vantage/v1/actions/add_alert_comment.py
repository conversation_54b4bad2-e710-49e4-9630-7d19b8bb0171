from typing import Dict

from apps.connectors.integrations.actions.add_alert_comment import (
    AddAlertComment,
    AddAlertCommentArgs,
    AddAlertCommentResult,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api import (
    NozomiVantageV1Api,
)


class NozomiVantageV1AddAlertComment(AddAlertComment):
    """Add comment action for Nozomi Vantage alerts."""

    name = "Add Alert Comment"

    def execute(self, args: AddAlertCommentArgs, **kwargs) -> AddAlertCommentResult:
        """
        Execute the add alert comment action.

        Args:
            args: Arguments containing alert ID and comment text
            **kwargs: Additional keyword arguments

        Returns:
            AddAlertCommentResult: Result of the comment addition
        """
        api: NozomiVantageV1Api = self.integration.get_api()

        alert_id = args.vendor_sync_id
        comment_text = args.comment

        # Add comment via API
        response = api.add_alert_comment(alert_id, comment_text)

        # Check if the response indicates success
        self._is_comment_added_successfully(response)

        return AddAlertCommentResult()

    def _is_comment_added_successfully(self, response: Dict) -> bool:
        """
        Check if the comment was added successfully based on API response.

        Args:
            response: API response from add comment request

        Returns:
            bool: True if comment was added successfully
        """
        # The exact response structure may need validation during implementation
        # Common patterns for success indication:
        if "data" in response:
            return True

        if "success" in response:
            return response["success"]

        if "status" in response:
            return response["status"] in ["success", "ok", "created"]

        # If we get a response without errors, assume success
        return True

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        """Return permission checks required for this action."""
        return []
