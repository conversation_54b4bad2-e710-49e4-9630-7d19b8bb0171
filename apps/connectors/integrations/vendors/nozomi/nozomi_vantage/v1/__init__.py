from apps.connectors.integrations import IntegrationActionType, TemplateVersion

from .bookmarks import NozomiVantageV1Bookmarks
from .connection import NozomiVantageV1Config, NozomiVantageV1Connection
from .integration import NozomiVantageV1Integration
from .settings import NozomiVantageV1Settings


class NozomiVantageV1TemplateVersion(TemplateVersion):
    integration = NozomiVantageV1Integration
    id = "v1"
    name = "v1"
    config_model = NozomiVantageV1Config
    connection_model = NozomiVantageV1Connection
    settings_model = NozomiVantageV1Settings
    bookmarks_model = NozomiVantageV1Bookmarks
    supported_actions = [
        IntegrationActionType.EVENT_SYNC,
        IntegrationActionType.ADD_ALERT_COMMENT,
        IntegrationActionType.UPDATE_LIFECYCLE_STATUS,
    ]
