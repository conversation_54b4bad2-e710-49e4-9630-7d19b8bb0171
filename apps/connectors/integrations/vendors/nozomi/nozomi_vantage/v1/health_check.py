from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckResult,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api import (
    NozomiVantageV1Api,
)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    """Health check for Nozomi Vantage API connection."""

    def get_result(self) -> IntegrationHealthCheckResult:
        """
        Perform health check by testing API connectivity and authentication.

        Returns:
            IntegrationHealthCheckResult: Result of the health check
        """
        api: NozomiVantageV1Api = self.integration.get_api()

        # Use the API's built-in health check method
        success = api.health_check()

        if success:
            return IntegrationHealthCheckResult.PASSED
        else:
            return IntegrationHealthCheckResult.FAILED
