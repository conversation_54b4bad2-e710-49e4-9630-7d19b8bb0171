from datetime import datetime
from typing import Dict, Generator, List, Optional
from urllib.parse import urljoin

from apps.connectors.integrations.api import ApiBase


class NozomiVantageV1ApiError(Exception):
    """Custom exception for Nozomi Vantage API errors"""

    pass


class NozomiVantageV1Api(ApiBase):
    """
    Nozomi Vantage API client for fetching alerts and managing authentication.

    API Documentation: Nozomi Vantage Swagger (available through partner/customer portal)
    """

    def __init__(self, url: str, api_key_name: str, api_key_token: str, **kwargs):
        """
        Initialize the Nozomi Vantage API client.

        Args:
            url: Base URL for the Nozomi Vantage API
            api_key_name: API key name for authentication
            api_key_token: API key token for authentication


        """
        # Validate required parameters
        if not url or not url.strip():
            raise ValueError("URL is required and cannot be empty")
        if not api_key_name or not api_key_name.strip():
            raise ValueError("API key name is required and cannot be empty")
        if not api_key_token or not api_key_token.strip():
            raise ValueError("API key token is required and cannot be empty")

        self.url = url.rstrip("/")
        self.api_key_name = api_key_name
        self.api_key_token = api_key_token
        self._bearer_token: Optional[str] = None

        # Configure session
        super().__init__(base_url=self.url)

        # Set default headers
        self.session.headers.update(
            {"Content-Type": "application/json", "Accept": "application/json"}
        )

    def _authenticate(self) -> str:
        """
        Authenticate with the Nozomi Vantage API and obtain a Bearer token.

        Returns:
            Bearer token for API authentication


        """
        auth_url = urljoin(self.url, "/api/v1/keys/sign_in")
        auth_data = {"name": self.api_key_name, "token": self.api_key_token}

        response = self.session.post(auth_url, json=auth_data)
        response.raise_for_status()

        auth_response = response.json()

        # Extract bearer token from response
        # The exact response structure may need validation during implementation
        if "data" in auth_response and "attributes" in auth_response["data"]:
            token = auth_response["data"]["attributes"].get("token")
        else:
            token = auth_response.get("token")

        if not token:
            raise NozomiVantageV1ApiError(
                "No bearer token found in authentication response"
            )

        self._bearer_token = token

        # Update session headers with bearer token
        self.session.headers.update({"Authorization": f"Bearer {token}"})

        return token

    def _ensure_authenticated(self):
        """Ensure the API client is authenticated with a valid bearer token."""
        if not self._bearer_token:
            self._authenticate()

    def get_alerts(
        self,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        page_size: int = 100,
        page_number: int = 1,
        **kwargs,
    ) -> Dict:
        """
        Fetch alerts from the Nozomi Vantage API.

        Args:
            since: Start time for alert filtering (optional)
            until: End time for alert filtering (optional)
            page_size: Number of alerts per page (default: 100)
            page_number: Page number for pagination (default: 1)
            **kwargs: Additional query parameters

        Returns:
            API response containing alerts data


        """
        self._ensure_authenticated()

        alerts_url = urljoin(self.url, "/api/v1/alerts")

        # Build query parameters
        params = {
            "page[size]": page_size,
            "page[number]": page_number,
        }

        # Add time filtering if provided
        if since:
            # Convert datetime to epoch milliseconds as expected by Nozomi API
            params["filter[time][gte]"] = int(since.timestamp() * 1000)

        if until:
            params["filter[time][lte]"] = int(until.timestamp() * 1000)

        # Add any additional parameters
        params.update(kwargs)

        response = self.session.get(alerts_url, params=params)
        response.raise_for_status()
        return response.json()

    def get_alert_details(self, alert_id: str) -> Dict:
        """
        Fetch detailed information for a specific alert.

        Args:
            alert_id: Unique identifier for the alert

        Returns:
            API response containing detailed alert information


        """
        self._ensure_authenticated()

        alert_url = urljoin(self.url, f"/api/v1/alerts/{alert_id}")

        response = self.session.get(alert_url)
        response.raise_for_status()
        return response.json()

    def paginate_alerts(
        self,
        since: Optional[datetime] = None,
        until: Optional[datetime] = None,
        page_size: int = 100,
        **kwargs,
    ) -> Generator[Dict, None, None]:
        """
        Generator that yields all alerts with automatic pagination handling.

        Args:
            since: Start time for alert filtering (optional)
            until: End time for alert filtering (optional)
            page_size: Number of alerts per page (default: 100)
            **kwargs: Additional query parameters

        Yields:
            Individual alert objects from the API response
        """
        page_number = 1

        while True:
            response = self.get_alerts(
                since=since,
                until=until,
                page_size=page_size,
                page_number=page_number,
                **kwargs,
            )

            # Extract alerts from response
            alerts = response.get("data", [])

            if not alerts:
                break

            for alert in alerts:
                yield alert

            # Check if there are more pages
            meta = response.get("meta", {})
            pagination = meta.get("pagination", {})

            if page_number >= pagination.get("pages", 1):
                break

            page_number += 1

    def get_alert_comments(self, alert_id: str) -> Dict:
        """
        Fetch comments for a specific alert.

        Args:
            alert_id: Unique identifier for the alert

        Returns:
            API response containing alert comments


        """
        self._ensure_authenticated()

        comments_url = urljoin(self.url, f"/api/v1/alerts/{alert_id}/comments")

        response = self.session.get(comments_url)
        response.raise_for_status()
        return response.json()

    def add_alert_comment(self, alert_id: str, comment_text: str) -> Dict:
        """
        Add a comment to a specific alert.

        Args:
            alert_id: Unique identifier for the alert
            comment_text: Text content of the comment

        Returns:
            API response confirming comment creation


        """
        self._ensure_authenticated()

        comments_url = urljoin(self.url, f"/api/v1/alerts/{alert_id}/comments")
        comment_data = {"text": comment_text}

        response = self.session.post(comments_url, json=comment_data)
        response.raise_for_status()
        return response.json()

    def acknowledge_alerts(
        self, alert_ids: List[str], acknowledge: bool = True
    ) -> Dict:
        """
        Acknowledge or unacknowledge alerts.

        Args:
            alert_ids: List of alert IDs to acknowledge/unacknowledge
            acknowledge: True to acknowledge, False to unacknowledge

        Returns:
            API response confirming acknowledgment status change


        """
        self._ensure_authenticated()

        ack_url = urljoin(self.url, "/api/open/alerts/ack")
        ack_data = {"ids": alert_ids, "value": acknowledge}

        response = self.session.post(ack_url, json=ack_data)
        response.raise_for_status()
        return response.json()

    def health_check(self) -> bool:
        """
        Perform a health check by attempting to authenticate and fetch a small number of alerts.

        Returns:
            True if the API is accessible and authentication works


        """
        # Test authentication
        self._authenticate()

        # Test basic API functionality by fetching a small number of alerts
        response = self.get_alerts(page_size=1, page_number=1)

        # Check if response has expected structure
        if "data" not in response:
            raise NozomiVantageV1ApiError("Unexpected API response structure")

        return True
