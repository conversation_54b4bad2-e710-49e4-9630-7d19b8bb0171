from typing import Dict, Optional

from pydantic import ConfigDict, Field

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.actions.external_ticket_actions import StateUpdate
from apps.connectors.integrations.template import TemplateVersionActionSettings


class JiraCreateIssueSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Jira Create Issue Settings")

    project_key: str = Field(
        title="Project Key",
        description="The key of the Jira project where issues will be created.",
        default="",
    )
    issue_type: str = Field(
        title="Issue Type",
        description="The Issue Type ID to create in Jira.",
        default="",
    )
    alert_id: Optional[str] = Field(
        title="Alert ID",
        description="Jira field to map the alert ID to. (Use dot notation for nested fields)",
        default="",
    )
    alert_title: Optional[str] = Field(
        title="Alert Title",
        description="Jira field to map the alert title to. (Use dot notation for nested fields)",
        default="",
    )
    alert_organization_name: Optional[str] = Field(
        title="Alert Organization",
        description="Jira field to map the alert's organization to. (Use dot notation for nested fields)",
        default="",
    )
    alert_category: Optional[str] = Field(
        title="Alert Category",
        description="Jira field to map the alert category to. (Use dot notation for nested fields)",
        default="",
    )
    alert_type: Optional[str] = Field(
        title="Alert Type",
        description="Jira field to map the alert's alert type to. (Use dot notation for nested fields)",
        default="",
    )
    alert_link: Optional[str] = Field(
        title="Alert Link",
        description="Jira field to map the alert's CORR link to. (Use dot notation for nested fields)",
        default="",
    )
    assigned_group: Optional[str] = Field(
        title="Assigned Group",
        description="Jira field to map the alert's assigned group to. (Use dot notation for nested fields)",
        default="",
    )
    integration_name: Optional[str] = Field(
        title="Integration Name",
        description="Jira field to map the alert's integration name to. (Use dot notation for nested fields)",
        default="",
    )
    soc_priority: Optional[str] = Field(
        title="SOC Priority",
        description="Jira field to map the alert's SOC priority to. (Use dot notation for nested fields)",
        default="",
    )
    vendor_severity: Optional[str] = Field(
        title="Vendor Severity",
        description="Jira field to map the alert's vendor severity to. (Use dot notation for nested fields)",
        default="",
    )
    alert_created_at: Optional[str] = Field(
        title="Alert Created At",
        description="Jira field to map the alert's creation date to. (Use dot notation for nested fields)",
        default="",
    )
    assigned_user_display: Optional[str] = Field(
        title="Assigned User",
        description="Jira field to map the alert's assigned user to. (Use dot notation for nested fields)",
        default="",
    )
    corr_status: Optional[str] = Field(
        title="CORR Status",
        description="Jira field to map the alert's CORR status to. (Use dot notation for nested fields)",
        default="",
    )


class JiraUpdateStatusSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Jira Update Issue Status Settings")

    jira_assignee_account_id: str = Field(
        title="Jira Assignee Account ID",
        description="The account ID of the user to assign the issue to in Jira.",
        default="",
    )
    status_mapping_assigned_to_customer_org: str = Field(
        title="Escalated to Customer",
        description="Status to transition in Jira for alerts assigned to the customer org. ex. New",
        default="",
    )
    status_mapping_assigned_to_customer_org_user: Optional[str] = Field(
        title="Customer User Assigned",
        description="Status to transition to in Jira for alerts assigned to a customer user. ex. In Progress",
        default="",
    )
    status_mapping_assigned_to_monitoring_org: str = Field(
        title="Escalated to CriticalStart",
        description="Status to transition to in Jira for alerts assigned to CriticalStart. ex. Awaiting Vendor",
        default="",
    )
    status_mapping_closed: str = Field(
        title="Closed",
        description="Status to transition to in Jira for closed alerts. ex. Resolved",
        default="",
    )

    @property
    def status_mapping(self) -> Dict[StateUpdate, str]:
        """
        Returns a mapping of StateUpdate to Jira status strings.
        """
        return {
            StateUpdate.ASSIGNED_TO_CUSTOMER_ORG: self.status_mapping_assigned_to_customer_org,
            StateUpdate.ASSIGNED_TO_CUSTOMER_ORG_USER: self.status_mapping_assigned_to_customer_org_user,
            StateUpdate.ASSIGNED_TO_MONITORING_ORG: self.status_mapping_assigned_to_monitoring_org,
            StateUpdate.CLOSED: self.status_mapping_closed,
        }

    @property
    def get_jira_assignee_account_id(self) -> str:
        """
        Returns the Jira assignee account ID.
        This is used to assign the issue to a specific user in Jira.
        """
        return (
            self.jira_assignee_account_id
            or self.integration.get_jira_assignee_account_id()
        )


class JiraSearchBulkIssuesSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Jira Update Issue Status Settings")

    project_key: str = Field(
        title="Project Key",
        description="The key of the Jira project to search for issues.",
        default="",
    )
    user_email: str = Field(
        title="Jira User Account Email",
        description="The user account email to be used for this integration.",
        default="",
    )
    corr_assignee: str = Field(
        title="CORR Assignee",
        description="The email of the user to assign the issue to in CORR.",
        default="",
    )
    status_mapping_assigned_to_customer_org: str = Field(
        title="Escalated to Customer",
        description="Status to transition in Jira for alerts assigned to the customer org. ex. New",
        default="",
    )
    status_mapping_assigned_to_customer_org_user: Optional[str] = Field(
        title="Customer User Assigned",
        description="Status to transition to in Jira for alerts assigned to a customer user. ex. In Progress",
        default="",
    )
    status_mapping_assigned_to_monitoring_org: str = Field(
        title="Escalated to CriticalStart",
        description="Status to transition to in Jira for alerts assigned to CriticalStart. ex. Awaiting Vendor",
        default="",
    )
    status_mapping_closed: str = Field(
        title="Closed",
        description="Status to transition to in Jira for closed alerts. ex. Resolved",
        default="",
    )

    @property
    def get_jira_status_to_state_mapping(self) -> Dict[str, StateUpdate]:
        """
        Returns a mapping of Jira status strings to StateUpdate enums.
        """
        return {
            self.status_mapping_assigned_to_customer_org: StateUpdate.ASSIGNED_TO_CUSTOMER_ORG,
            self.status_mapping_assigned_to_customer_org_user: StateUpdate.ASSIGNED_TO_CUSTOMER_ORG_USER,
            self.status_mapping_assigned_to_monitoring_org: StateUpdate.ASSIGNED_TO_MONITORING_ORG,
            self.status_mapping_closed: StateUpdate.CLOSED,
        }


JiraV1Settings = create_settings_model(
    "JiraV1Settings",
    {
        IntegrationActionType.CREATE_EXTERNAL_TICKET: JiraCreateIssueSettings,
        IntegrationActionType.UPDATE_EXTERNAL_TICKET_STATUS: JiraUpdateStatusSettings,
        IntegrationActionType.SEARCH_EXTERNAL_TICKET: JiraSearchBulkIssuesSettings,
    },
)
