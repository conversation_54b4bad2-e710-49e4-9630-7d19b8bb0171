from typing import List

from django.utils.dateparse import parse_datetime

from apps.connectors.integrations.actions.external_ticket_actions import (
    AttachmentFields,
    ExternalTicketFields,
    SearchExternalTicket,
    SearchExternalTicketArgs,
    SearchExternalTicketResult,
)
from apps.connectors.integrations.vendors.atlassian.jira.utils import (
    extract_jira_fields,
)
from apps.connectors.integrations.vendors.atlassian.jira.v1.settings import (
    JiraSearchBulkIssuesSettings,
)


class JiraV1SearchBulkIssues(SearchExternalTicket):
    settings = JiraSearchBulkIssuesSettings

    def execute(
        self, args: SearchExternalTicketArgs, **kwargs
    ) -> SearchExternalTicketResult:
        api = self.integration.get_api()

        project_key = self.settings.project_key
        user_email = self.settings.user_email

        jql = f'project = "{project_key}" AND reporter = "{user_email}" AND updatedDate >= "{args.updated_at}" ORDER BY createdDate DESC'

        issues = api.search_issues_bulk(jql=jql)
        extracted_issues = self.get_issue_details(
            issues=issues,
        )
        return SearchExternalTicketResult(search_result=extracted_issues)

    def get_permission_checks(self):
        return []

    def get_issue_details(self, issues: List) -> List[ExternalTicketFields]:
        """
        Extracts relevant details from Jira issues, including comments, attachments, and status updates.
        """
        api = self.integration.get_api()
        extracted_data = []

        status_mapping = self.settings.get_jira_status_to_state_mapping
        corr_assignee = self.settings.corr_assignee
        for issue in issues:
            issue_data, raw_attachments = extract_jira_fields(
                issue, status_mapping, corr_assignee
            )

            # Extract attachments and get attachment content for each attachment
            attachments = []
            for attachment in raw_attachments:
                attachment_id = attachment.get("id")
                attachments.append(
                    AttachmentFields(
                        attachment_id=attachment_id,
                        created_at=parse_datetime(attachment.get("created")),
                        file_name=attachment.get("filename"),
                        file_content=api.get_attachment_content(attachment_id),
                    )
                )
            issue_data.attachments = attachments
            extracted_data.append(issue_data)

        return extracted_data
