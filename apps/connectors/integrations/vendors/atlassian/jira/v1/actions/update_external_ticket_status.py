from apps.connectors.integrations.actions.external_ticket_actions import (
    UpdateExternalTicketStatus,
    UpdateExternalTicketStatusArgs,
    UpdateExternalTicketStatusResult,
)
from apps.connectors.integrations.vendors.atlassian.jira.v1.settings import (
    JiraUpdateStatusSettings,
)


class JiraV1UpdateIssueStatus(UpdateExternalTicketStatus):
    settings = JiraUpdateStatusSettings

    def execute(
        self, args: UpdateExternalTicketStatusArgs, **kwargs
    ) -> UpdateExternalTicketStatusResult:
        self.validate_status_mapping()
        api = self.integration.get_api()
        # Assign the issue to the account ID when the state is ASSIGNED_TO_CUSTOMER_ORG_USER
        if account_id := self.settings.get_jira_assignee_account_id:
            api.assign_issue(issue_id_or_key=args.ticket_id, account_id=account_id)

        # Update the issue status
        status = self.settings.status_mapping.get(args.state)
        api.update_issue_status(issue_id_or_key=args.ticket_id, status=status)
        return UpdateExternalTicketStatusResult()

    def get_permission_checks(self):
        return []

    def validate_status_mapping(self):
        """
        Validate the status mapping provided in settings against the Jira API.
        """
        api = self.integration.get_api()
        all_statuses = api.get_all_statuses().values()
        for mapping in self.settings.status_mapping.values():
            if mapping not in all_statuses:
                raise ValueError(
                    f"Status mapping '{mapping}' is not valid. "
                    "Please check the status mapping in the integration settings."
                )
