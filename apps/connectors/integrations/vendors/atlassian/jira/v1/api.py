import json
import logging

from requests.auth import HTTP<PERSON>asicAuth

from apps.connectors.integrations.api import ApiBase

logger = logging.getLogger(__name__)


class JiraV1Api(ApiBase):
    API_VERSION = "3"

    def __init__(
        self,
        api_token,
        base_url,
        user_email,
    ):
        self.token = api_token
        self.user = user_email

        self.base_url = f"{base_url}/rest/api/{self.API_VERSION}"
        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
        }

        super().__init__(base_url=self.base_url)

        self.session.auth = HTTPBasicAuth(self.user, self.token)

    def get_issue(self, issue_id_or_key, params=None):
        """
        Get a Jira issue by its ID.
        """
        url = f"{self.base_url}/issue/{issue_id_or_key}"
        response = self.session.get(
            url, headers=self.headers, params=params if params else {}
        )
        return response.json()

    def create_issue(self, issue_fields, update_history=False):
        """
        Create a Jira issue with the provided fields.
        """
        params = {"updateHistory": "true" if update_history else "false"}
        data = json.dumps({"fields": issue_fields})

        response = self.session.post(
            f"{self.base_url}/issue",
            data=data,
            headers=self.headers,
            params=params,
        )
        return {"id": response.json()["id"], "key": response.json()["key"]}

    def assign_issue(self, issue_id_or_key, account_id):
        """
        Assign a Jira issue to a user using the account ID.
        """
        data = json.dumps({"accountId": account_id})
        response = self.session.put(
            f"{self.base_url}/issue/{issue_id_or_key}/assignee",
            data=data,
            headers=self.headers,
        )

        return response

    def update_issue_status(self, issue_id_or_key, status):
        """
        Update the status of a Jira issue via transition.
        """
        transition_id = self.get_transition_id_for_status(issue_id_or_key, status)
        if not transition_id:
            raise ValueError(f"No transition ID for status: {status}")

        response = self.session.post(
            f"{self.base_url}/issue/{issue_id_or_key}/transitions",
            data=json.dumps({"transition": {"id": transition_id}}),
            headers=self.headers,
        )
        return response

    def add_comment_to_issue(self, issue_id_or_key, comment_body):
        """
        Add a comment to a Jira issue
        """
        response = self.session.post(
            f"{self.base_url}/issue/{issue_id_or_key}/comment",
            data=json.dumps({"body": comment_body}),
            headers=self.headers,
        )
        return {"id": response.json()["id"]}

    def add_attachment_to_issue(self, issue_id_or_key, file_name, content):
        """
        Add an attachment to a Jira issue.
        """
        files = {"file": (file_name, content, "application-type")}
        response = self.session.post(
            url=f"{self.base_url}/issue/{issue_id_or_key}/attachments",
            files=files,
            headers={"X-Atlassian-Token": "no-check"},
        )

        return response.json()[0]

    def get_attachment_content(self, attachment_id):
        """
        Get the content of a Jira attachment by its ID.
        """
        response = self.session.get(
            f"{self.base_url}/attachment/content/{attachment_id}",
            headers=self.headers,
        )
        return response.content

    def search_issues_bulk(self, jql, max_results=100):
        """
        Get a list of Jira issues based on JQL query.
        Handles pagination using nextPageToken.
        """
        issues = []

        params = {
            "jql": jql,
            "maxResults": max_results,
            "fields": "statusCategory,created,creator,updated,attachment,comment,statuscategorychangedate",
        }

        while True:
            response = self.session.get(
                f"{self.base_url}/search/jql", params=params, headers=self.headers
            )
            data = response.json()
            issues.extend(data.get("issues", []))
            params["next_page_token"] = data.get("nextPageToken")

            if data.get("isLast", True):
                break

        return issues

    def get_all_statuses(self):
        """
        Get all Jira statuses.
        """
        statuses = {}
        response = self.session.get(f"{self.base_url}/status", headers=self.headers)
        for status in response.json():
            statuses[status["id"]] = status["name"]

        return statuses

    def get_transitions_for_issue(self, issue_id_or_key):
        """
        Get a Jira issue transitions by ID.
        """
        response = self.session.get(
            f"{self.base_url}/issue/{issue_id_or_key}/transitions", headers=self.headers
        )
        return response.json()["transitions"]

    def get_transition_id_for_status(self, issue_id_or_key, status):
        """
        Get a Jira issue transition ID for a given status.
        Return the ID of the transition which transitions to the required status name.
        """
        transitions = self.get_transitions_for_issue(issue_id_or_key)
        for transition in transitions:
            if transition["to"]["name"] == status:
                return transition["id"]

        return None
