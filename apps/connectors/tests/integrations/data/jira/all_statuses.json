[{"self": "https://cs-dev.atlassian.net/rest/api/3/status/1", "description": "The work item is open and ready for the assignee to start work on it.", "iconUrl": "https://cs-dev.atlassian.net/images/icons/statuses/open.png", "name": "Open", "untranslatedName": "Open", "id": "1", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/2", "id": 2, "key": "new", "colorName": "blue-gray", "name": "To Do"}}, {"self": "https://cs-dev.atlassian.net/rest/api/3/status/3", "description": "This work item is being actively worked on at the moment by the assignee.", "iconUrl": "https://cs-dev.atlassian.net/images/icons/statuses/inprogress.png", "name": "In Progress", "untranslatedName": "In Progress", "id": "3", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/4", "id": 4, "key": "indeterminate", "colorName": "yellow", "name": "In Progress"}}, {"self": "https://cs-dev.atlassian.net/rest/api/3/status/6", "description": "The work item is considered finished, the resolution is correct. work items which are closed can be reopened.", "iconUrl": "https://cs-dev.atlassian.net/images/icons/statuses/closed.png", "name": "Closed", "untranslatedName": "Closed", "id": "6", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/3", "id": 3, "key": "done", "colorName": "green", "name": "Done"}}, {"self": "https://cs-dev.atlassian.net/rest/api/3/status/10010", "description": "", "iconUrl": "https://cs-dev.atlassian.net/", "name": "To Do", "untranslatedName": "To Do", "id": "10010", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/2", "id": 2, "key": "new", "colorName": "blue-gray", "name": "To Do"}, "scope": {"type": "PROJECT", "project": {"id": "10033"}}}, {"self": "https://cs-dev.atlassian.net/rest/api/3/status/10012", "description": "", "iconUrl": "https://cs-dev.atlassian.net/", "name": "Done", "untranslatedName": "Done", "id": "10012", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/3", "id": 3, "key": "done", "colorName": "green", "name": "Done"}, "scope": {"type": "PROJECT", "project": {"id": "10033"}}}, {"self": "https://cs-dev.atlassian.net/rest/api/3/status/10013", "description": "", "iconUrl": "https://cs-dev.atlassian.net/", "name": "Awaiting <PERSON><PERSON><PERSON>", "untranslatedName": "Awaiting <PERSON><PERSON><PERSON>", "id": "10013", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/3", "id": 3, "key": "done", "colorName": "green", "name": "Awaiting <PERSON><PERSON><PERSON>"}, "scope": {"type": "PROJECT", "project": {"id": "10033"}}}]