"""
Tests for Proofpoint V1 integration.

This test module uses JSON data files stored in the data/proofpoint directory
rather than hardcoding test data directly in the test file. This approach:

1. Centralizes test data in dedicated files
2. Makes the test code cleaner and more maintainable
3. Allows for easier updates to test data
4. Follows the pattern used in other integration tests

Data files are loaded via a cached function to improve performance.
"""

import json
import re
from datetime import datetime, timedelta, timezone
from functools import cache
from unittest import mock

import responses
from responses.matchers import json_params_matcher

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.actions.decode_url import DecodeUrlArgs
from apps.connectors.integrations.health_check import IntegrationHealthCheckResult
from apps.connectors.integrations.schemas import EmailThreatIdentifierArgs
from apps.connectors.integrations.schemas.identifiers import (
    EmailThreatIdentifier,
    UrlIdentifier,
)
from apps.connectors.integrations.schemas.ocsf import (
    OSINT,
    Campaign,
    Confidence,
    OSINTIndicatorType,
)
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.actions.event_sync import (
    convert_click_classification,
    convert_message_to_ocsf,
)
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.actions.get_threat_details import (
    ProofpointV1GetThreatDetails,
    map_threat_status_to_confidence,
    map_threat_type_to_osint_type,
    normalize_threat_actors,
    normalize_threat_to_osint,
)
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.bookmarks import (
    ProofpointV1Bookmarks,
)
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.health_check import (
    ConnectionHealthCheck,
)
from apps.connectors.utils import serialize
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory


@cache
def load_data(filename):
    """Load test data from JSON files."""
    path = "apps/connectors/tests/integrations/data/proofpoint"
    with open(f"{path}/{filename}.json", "r") as f:
        return json.load(f)


def response_clicks_events(type="clicksBlocked", classification="MALWARE"):
    """Get click event test data based on type and classification."""
    if type == "clicksPermitted":
        if classification == "PHISH":
            data = load_data("clicks_permitted_phish")
        elif classification == "SPAM":
            data = load_data("clicks_permitted_spam")
        else:
            # Default to PHISH for other classifications in permitted clicks
            data = load_data("clicks_permitted_phish")
    else:
        # Default to blocked clicks
        data = load_data("clicks_blocked")

    # Modify the data with the provided parameters
    data = dict(data)  # Create a copy to avoid modifying cached data
    data["type"] = type
    data["classification"] = classification
    return data


def response_messages_events(type="messagesBlocked"):
    """Get message event test data based on type."""
    if type == "messagesBlocked":
        data = load_data("messages_blocked")
    else:
        # Default to messagesDelivered for other types
        data = load_data("messages_delivered")

    # Modify the data with the provided type parameter
    data = dict(data)  # Create a copy to avoid modifying cached data
    data["type"] = type
    return data


def expected_message_response(alternative=True, type="messagesBlocked"):
    """Generate expected message response based on the loaded test data."""
    event_data = response_messages_events(type)

    # Generate title and external_id from threat info map
    threat_info_map = event_data.get("threatsInfoMap", [])
    classifications_set = set(t["classification"] for t in threat_info_map)
    threat_types_set = set(t["threatType"] for t in threat_info_map)
    sorted_classifications = sorted(classifications_set)
    sorted_threat_types = sorted(threat_types_set)

    title = f"Suspicious Email Detected - {'.'.join(sorted_classifications)} via {'.'.join(sorted_threat_types)}"
    external_id = ".".join(sorted_classifications + sorted_threat_types)

    # Create OCSF osint entries from the threat info map
    osint_entries = []
    for threat_info in threat_info_map:
        threat_type = threat_info.get("threatType", "")
        if threat_type.lower() == "attachment":
            type_name = "File"
            type_id = 11
        elif threat_type.lower() == "url":
            type_name = "URL"
            type_id = 5
        elif threat_type.lower() == "message":
            type_name = "Email"
            type_id = 8
        else:
            type_name = threat_type
            type_id = 99

        osint_entries.append(
            {
                "category": threat_info.get("classification"),
                "src_url": threat_info.get("threatUrl"),
                "type": type_name,
                "type_id": type_id,
                "uid": threat_info.get("threatID"),
                "value": threat_info.get("threat"),
            }
        )

    # Format the event timestamp
    event_timestamp = event_data.get("messageTime", "").replace(".000Z", "Z")

    # Convert timestamp to epoch milliseconds for the 'time' field
    timestamp_obj = datetime.strptime(event_timestamp, "%Y-%m-%dT%H:%M:%SZ")
    timestamp_epoch_ms = int(
        timestamp_obj.replace(tzinfo=timezone.utc).timestamp() * 1000
    )

    # Prepare HTTP headers from the event data
    http_headers = []
    if event_data.get("sender"):
        http_headers.append(
            {
                "name": "Sender",
                "value": event_data.get("sender"),
            }
        )
    if event_data.get("xmailer"):
        http_headers.append(
            {
                "name": "X-Mailer",
                "value": event_data.get("xmailer"),
            }
        )

    # Determine action and disposition based on message type and alternative flag
    is_blocked = "Blocked" in type
    action = "Denied" if (is_blocked or alternative) else "Allowed"
    action_id = 2 if (is_blocked or alternative) else 1
    disposition = "Blocked" if (is_blocked or alternative) else "Allowed"
    disposition_id = 2 if (is_blocked or alternative) else 1

    # Process message parts for files
    files = []
    for part in event_data.get("messageParts", []):
        files.append(
            {
                "name": part.get("filename"),
                "hashes": [
                    {
                        "algorithm": "MD5",
                        "algorithm_id": 1,
                        "value": part.get("md5"),
                    },
                    {
                        "algorithm": "SHA-256",
                        "algorithm_id": 3,
                        "value": part.get("sha256"),
                    },
                ],
                "mime_type": part.get("oContentType"),
                "security_descriptor": part.get("sandboxStatus"),
            }
        )

    # Build the full response
    return {
        "raw_event": event_data,
        "event_timestamp": event_timestamp,
        "ioc": {
            "external_id": external_id,
            "external_name": title,
            "has_ioc_definition": False,
            "mitre_techniques": None,
        },
        "vendor_item_ref": None,
        "vendor_group_ref": None,
        "ocsf": {
            "osint": osint_entries,
            "activity_id": 2,
            "activity_name": "Receive",
            "category_name": "Network Activity",
            "category_uid": 4,
            "class_name": "Email Activity",
            "class_uid": 4009,
            "action": action,
            "action_id": action_id,
            "disposition": disposition,
            "disposition_id": disposition_id,
            "message": title,
            "metadata": {
                "correlation_uid": event_data.get("GUID"),
                "event_code": None,
                "profiles": ["datetime", "osint", "security_control"],
                "uid": event_data.get("GUID"),
                "version": "1.5.0-dev",
            },
            "time": timestamp_epoch_ms,
            "time_dt": event_timestamp,
            "type_name": "Email Activity: Receive",
            "type_uid": 400902,
            "email": {
                "cc": event_data.get("ccAddresses", []),
                "files": files,
                "from": event_data.get("fromAddress"),
                "from_mailbox": event_data.get("headerFrom"),
                "http_headers": http_headers,
                "message_uid": event_data.get("messageID"),
                "size": None,
                "reply_to_mailboxes": event_data.get("headerReplyTo", []) or [],
                "subject": event_data.get("subject"),
                "to": event_data.get("recipient", []),
                "uid": event_data.get("GUID"),
                "x_originating_ip": (
                    [event_data.get("senderIP")] if event_data.get("senderIP") else []
                ),
            },
        },
    }


def expected_click_response(
    alternative=True,
    type="clicksBlocked",
    classification="MALWARE",
):
    """Generate expected click response based on the loaded test data."""
    event_data = response_clicks_events(type, classification)

    # Generate the title and determine URL classification
    title = "Suspicious URL click detected - " + classification.upper()
    category = convert_click_classification(classification)
    if isinstance(category, str):
        category_id = 99
        category_name = category
    else:
        category_id = category.id
        category_name = category.name

    # Format the event timestamp
    event_timestamp = event_data.get("clickTime", "").replace(".000Z", "Z")

    # Convert timestamp to epoch milliseconds for the 'time' field
    timestamp_obj = datetime.strptime(event_timestamp, "%Y-%m-%dT%H:%M:%SZ")
    timestamp_epoch_ms = int(
        timestamp_obj.replace(tzinfo=timezone.utc).timestamp() * 1000
    )

    # Parse URL to extract components
    url = event_data.get("url", "")
    url_parts = url.split("://")
    scheme = url_parts[0] if len(url_parts) > 1 else "http"
    remaining = url_parts[1] if len(url_parts) > 1 else url
    hostname = remaining.split("/")[0] if "/" in remaining else remaining
    path = "/" + "/".join(remaining.split("/")[1:]) if "/" in remaining else "/"

    # Determine activity, action, and disposition based on click type
    is_blocked = "Blocked" in type
    activity_name = "Refuse" if (is_blocked or alternative) else "Open"
    activity_id = 5 if (is_blocked or alternative) else 1
    action = "Denied" if (is_blocked or alternative) else "Allowed"
    action_id = 2 if (is_blocked or alternative) else 1
    disposition = "Blocked" if (is_blocked or alternative) else "Allowed"
    disposition_id = 2 if (is_blocked or alternative) else 1

    # Create osint entries with correct structure
    osint_entry = {
        "category": classification,
        "src_url": event_data.get("threatURL"),
        "type": "URL",
        "type_id": 5,
        "uid": event_data.get("threatID"),
        "value": event_data.get("url"),
        "email": {
            "from": event_data.get("sender"),
            "to": [event_data.get("recipient")],
            "x_originating_ip": (
                [event_data.get("senderIP")] if event_data.get("senderIP") else []
            ),
        },
    }

    return {
        "raw_event": event_data,
        "event_timestamp": event_timestamp,
        "ioc": {
            "external_id": classification,
            "external_name": title,
            "has_ioc_definition": False,
            "mitre_techniques": None,
        },
        "vendor_item_ref": None,
        "vendor_group_ref": None,
        "ocsf": {
            "osint": [osint_entry],
            "app_name": event_data.get("userAgent"),
            "activity_name": activity_name,
            "activity_id": activity_id,
            "action": action,
            "action_id": action_id,
            "category_name": "Network Activity",
            "category_uid": 4,
            "class_name": "Network Activity",
            "class_uid": 4001,
            "disposition": disposition,
            "disposition_id": disposition_id,
            "src_endpoint": {"ip": event_data.get("clickIP")},
            "message": title,
            "metadata": {
                "correlation_uid": event_data.get("GUID"),
                "event_code": None,
                "profiles": ["datetime", "osint", "security_control"],
                "uid": event_data.get("GUID"),
                "version": "1.5.0-dev",
            },
            "time": timestamp_epoch_ms,
            "time_dt": event_timestamp,
            "type_name": f"Network Activity: {activity_name}",
            "type_uid": 400105 if (is_blocked or alternative) else 400101,
            "url": {
                "categories": [category_name],
                "category_ids": [category_id],
                "hostname": hostname,
                "netloc": hostname,
                "path": path,
                "scheme": scheme,
                "url_string": event_data.get("url"),
            },
        },
    }


def setup_responses(fail=False, query_end_time=None):
    """Set up mock API responses for Proofpoint SIEM events."""
    if fail:
        responses.add(
            responses.GET,
            re.compile(
                r"https://test_url\.com/v2/siem/all\?format=json&interval=.*?/.*?"
            ),
            json={"error": "Invalid request"},
            status=400,
        )
        return

    if query_end_time is None:
        query_end_time = datetime.now(timezone.utc) - timedelta(minutes=5)

    responses.get(
        re.compile(r"https://test_url\.com/v2/siem/all\?format=json&interval=.*?/.*?"),
        json={
            "queryEndTime": query_end_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "messagesBlocked": [
                response_messages_events("messagesBlocked"),
                response_messages_events("messagesBlocked"),
            ],
            "messagesDelivered": [
                response_messages_events("messagesDelivered"),
                response_messages_events("messagesDelivered"),
            ],
            "clicksPermitted": [
                response_clicks_events("clicksPermitted", "PHISH"),
                response_clicks_events("clicksPermitted", "SPAM"),
            ],
            "clicksBlocked": [
                response_clicks_events("clicksBlocked", "MALWARE"),
                response_clicks_events("clicksBlocked", "MALWARE"),
            ],
        },
    )


class ProofpointV1HealthCheckTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.integration = ConnectorFactory.get_integration(
            technology_id="proofpoint",
        )

    @responses.activate
    def test_test_connection(self):
        setup_responses()
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_test_connection_invalid(self):
        setup_responses(fail=True)
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)


class ProofpointV1ApiTest(BaseTestCase):
    def setUp(self):
        super().setUp()

    @responses.activate
    def test_get_all_siem_events(self):
        api = ConnectorFactory.get_api(technology_id="proofpoint")
        setup_responses()
        response = api.get_all_siem_events(
            from_datetime="2025-03-17T07:25:47Z",
            to_datetime="2025-03-17T08:25:47Z",
        )
        self.assertEqual(
            response,
            {
                "queryEndTime": mock.ANY,
                "messagesBlocked": [
                    response_messages_events(),
                    response_messages_events(),
                ],
                "messagesDelivered": [
                    response_messages_events(type="messagesDelivered"),
                    response_messages_events(type="messagesDelivered"),
                ],
                "clicksPermitted": [
                    response_clicks_events(
                        type="clicksPermitted", classification="PHISH"
                    ),
                    response_clicks_events(
                        type="clicksPermitted", classification="SPAM"
                    ),
                ],
                "clicksBlocked": [
                    response_clicks_events(),
                    response_clicks_events(),
                ],
            },
        )

    @responses.activate
    def test_get_email_threat(self):
        api = ConnectorFactory.get_api(technology_id="proofpoint")
        threat_id = "2fab740f143fc1aa4c1cd0146d334c5593b1428f6d062b2c406e5efe8abe95ca"
        expected_response = load_data("threat_summary")["malware_attachment_threat"]

        responses.add(
            responses.GET,
            api.url(f"/v2/threat/summary/{threat_id}"),
            json=expected_response,
            status=200,
        )

        response = api.get_email_threat(threat_id)
        self.assertEqual(response, expected_response)

    @responses.activate
    def test_decode_url(self):
        api = ConnectorFactory.get_api(technology_id="proofpoint")
        urls_to_decode = ["http://proofpoint.com/xyz"]
        expected_response = load_data("decoded_urls")

        responses.add(
            responses.POST,
            api.url("/v2/url/decode"),
            json=expected_response,
            match=[json_params_matcher({"urls": urls_to_decode})],
            status=200,
        )

        response = api.decode_url(urls_to_decode)
        self.assertEqual(response, expected_response)

    @responses.activate
    def test_get_threat_forensics(self):
        api = ConnectorFactory.get_api(technology_id="proofpoint")
        threat_id = "67890"
        expected_response = {"forensics": [{"event": "malware_execution"}]}

        responses.add(
            responses.GET,
            api.url(f"/v2/forensics?threatId={threat_id}"),
            json=expected_response,
            status=200,
        )

        response = api.get_threat_forensics(threat_id)
        self.assertEqual(response, expected_response)


class ProofpointV1GetThreatDetailsTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.integration = ConnectorFactory.get_integration(technology_id="proofpoint")

    @responses.activate
    def test_get_threat_details_malware_attachment(self):
        """Test threat details retrieval for malware attachment with full data."""
        threat_id = "2fab740f143fc1aa4c1cd0146d334c5593b1428f6d062b2c406e5efe8abe95ca"
        threat_data = load_data("threat_summary")["malware_attachment_threat"]

        responses.add(
            responses.GET,
            f"https://test_url.com/v2/threat/summary/{threat_id}",
            json=threat_data,
            status=200,
        )

        from apps.connectors.integrations.schemas import EmailThreatIdentifier, EmailThreatIdentifierArgs
        from apps.connectors.integrations import IntegrationActionType

        threat_identity = EmailThreatIdentifier(
            value_type="proofpoint",
            value=threat_id,
        )
        args = EmailThreatIdentifierArgs(email_threat_id=threat_identity)

        result = self.integration.invoke_action(
            IntegrationActionType.GET_THREAT_DETAILS, args
        )

        osint_data = result.result
        self.assertEqual(osint_data.uid, threat_id)
        self.assertEqual(osint_data.value, "Malicious PDF Document - Banking Trojan")
        self.assertEqual(osint_data.category, "malware")
        self.assertEqual(osint_data.vendor_name, "Proofpoint")
        self.assertIn("Category: malware", osint_data.comment)
        self.assertIn("Status: active", osint_data.comment)
        self.assertIn("Type: attachment", osint_data.comment)
        self.assertIn("Severity: 850", osint_data.comment)
        self.assertIn("Associated threat actors: dash_actor_05cbc5", osint_data.comment)
        self.assertIn("Threat Families: Banking Trojan", osint_data.comment)
        self.assertIn("Malware: Ursnif", osint_data.comment)
        self.assertIn("Techniques: XL4 macros", osint_data.comment)
        self.assertIn("Brands: DocuSign", osint_data.comment)

    @responses.activate
    def test_get_threat_details_phishing_url(self):
        """Test threat details retrieval for phishing URL."""
        threat_id = "8e7d2c1b9f4a3e6d5c8b7a9e2f1d4c6b8a5e3f7c9b2d6a8e1f4c7b9a2e5d8c1b"
        threat_data = load_data("threat_summary")["phishing_url_threat"]

        responses.add(
            responses.GET,
            f"https://test_url.com/v2/threat/summary/{threat_id}",
            json=threat_data,
            status=200,
        )

        from apps.connectors.integrations.schemas import EmailThreatIdentifier, EmailThreatIdentifierArgs
        from apps.connectors.integrations import IntegrationActionType

        threat_identity = EmailThreatIdentifier(
            value_type="proofpoint",
            value=threat_id,
        )
        args = EmailThreatIdentifierArgs(email_threat_id=threat_identity)

        result = self.integration.invoke_action(
            IntegrationActionType.GET_THREAT_DETAILS, args
        )

        osint_data = result.result
        self.assertEqual(osint_data.uid, threat_id)
        self.assertEqual(osint_data.value, "Phishing URL - Credential Harvesting")
        self.assertEqual(osint_data.category, "phish")
        self.assertIn("Category: phish", osint_data.comment)
        self.assertIn("Status: active", osint_data.comment)
        self.assertIn("Type: url", osint_data.comment)
        self.assertIn("Severity: 720", osint_data.comment)
        self.assertIn("Associated threat actors: phishing_group_alpha", osint_data.comment)
        self.assertIn("credential_harvester_beta", osint_data.comment)

    @responses.activate
    def test_get_threat_details_minimal_data(self):
        """Test threat details retrieval with minimal data."""
        threat_id = "minimal123"
        threat_data = load_data("threat_summary")["minimal_threat"]

        responses.add(
            responses.GET,
            f"https://test_url.com/v2/threat/summary/{threat_id}",
            json=threat_data,
            status=200,
        )

        from apps.connectors.integrations.schemas import EmailThreatIdentifier, EmailThreatIdentifierArgs
        from apps.connectors.integrations import IntegrationActionType

        threat_identity = EmailThreatIdentifier(
            value_type="proofpoint",
            value=threat_id,
        )
        args = EmailThreatIdentifierArgs(email_threat_id=threat_identity)

        result = self.integration.invoke_action(
            IntegrationActionType.GET_THREAT_DETAILS, args
        )

        osint_data = result.result
        self.assertEqual(osint_data.uid, threat_id)
        self.assertEqual(osint_data.value, "Minimal Threat Data")
        self.assertEqual(osint_data.category, "malware")
        self.assertIn("Category: malware", osint_data.comment)
        self.assertIn("Status: active", osint_data.comment)
        self.assertIn("Type: attachment", osint_data.comment)


class ProofpointV1IntegrationTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.integration = ConnectorFactory.get_integration(technology_id="proofpoint")

    def test_bookmarks(self):
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)

        schema = ProofpointV1Bookmarks.model_json_schema()
        self.assertIn(IntegrationActionType.EVENT_SYNC.value, schema["properties"])

        schema = schema["properties"][IntegrationActionType.EVENT_SYNC.value]
        self.assertIn("query_end_time", schema["properties"])

    @responses.activate
    def test_event_sync(self):
        setup_responses()
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)
        end_time = datetime.now(timezone.utc) - timedelta(minutes=30)
        bookmark.query_end_time = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            **{
                "bookmark": bookmark,
            },
        )

        result = serialize(list(response))
        self.assertEqual(len(result), 8)
        self.assertEqual(
            result,
            [
                expected_message_response(),
                expected_message_response(),
                expected_message_response(type="messagesDelivered", alternative=False),
                expected_message_response(type="messagesDelivered", alternative=False),
                expected_click_response(
                    type="clicksPermitted",
                    alternative=False,
                    classification="PHISH",
                ),
                expected_click_response(
                    type="clicksPermitted",
                    alternative=False,
                    classification="SPAM",
                ),
                expected_click_response(),
                expected_click_response(),
            ],
        )

    @responses.activate
    def test_event_sync_multiple_hours(self):
        setup_responses()
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)
        end_time = datetime.now(timezone.utc) - timedelta(minutes=90)
        bookmark.query_end_time = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            **{
                "bookmark": bookmark,
            },
        )

        result = serialize(list(response))
        self.assertEqual(len(result), 16)

    @responses.activate
    def test_event_sync_seconds_granularity(self):
        setup_responses(query_end_time=datetime.now(timezone.utc))
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)
        end_time = datetime.now(timezone.utc) - timedelta(hours=1)
        bookmark.query_end_time = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            **{
                "bookmark": bookmark,
            },
        )

        result = serialize(list(response))
        self.assertEqual(len(result), 8)

    @responses.activate
    def test_ocsf_normalization(self):
        event = response_messages_events()
        event["fromAddress"] = ["<EMAIL>"]

        ocsf = convert_message_to_ocsf(event, event["type"], "test_title")
        self.assertTrue(ocsf.email.from_ == "<EMAIL>")

    @responses.activate
    def test_decode_url(self):
        # Load test data for decoded URL
        decoded_url_data = load_data("decoded_url")

        responses.post(
            "https://test_url.com/v2/url/decode",
            json=decoded_url_data,
        )

        result = self.integration.invoke_action(
            IntegrationActionType.DECODE_URL,
            action_args=DecodeUrlArgs(
                url=UrlIdentifier(value=decoded_url_data["encodedUrl"])
            ),
        )

        self.assertIsNotNone(result)
        self.assertEqual(result.result.url_string, decoded_url_data["decodedUrl"])

    # ========== Threat Intelligence Tests ==========

    def test_map_threat_type_to_osint_type(self):
        """Test threat type mapping to OCSF OSINT indicator types."""
        # Test standard mappings (using lowercase as per official API)
        self.assertEqual(
            map_threat_type_to_osint_type("attachment"), OSINTIndicatorType.FILE
        )
        self.assertEqual(
            map_threat_type_to_osint_type("url"), OSINTIndicatorType.URL
        )
        self.assertEqual(
            map_threat_type_to_osint_type("message text"), OSINTIndicatorType.EMAIL
        )
        self.assertEqual(
            map_threat_type_to_osint_type("hash"), OSINTIndicatorType.HASH
        )
        self.assertEqual(
            map_threat_type_to_osint_type("file"), OSINTIndicatorType.FILE
        )

        # Test case insensitive mapping (uppercase should still work)
        self.assertEqual(
            map_threat_type_to_osint_type("ATTACHMENT"), OSINTIndicatorType.FILE
        )
        self.assertEqual(
            map_threat_type_to_osint_type("URL"), OSINTIndicatorType.URL
        )

        # Test unknown/other types
        self.assertEqual(
            map_threat_type_to_osint_type("UNKNOWN"), OSINTIndicatorType.OTHER
        )
        self.assertEqual(
            map_threat_type_to_osint_type(""), OSINTIndicatorType.OTHER
        )
        self.assertEqual(
            map_threat_type_to_osint_type(None), OSINTIndicatorType.OTHER
        )

    def test_map_threat_status_to_confidence(self):
        """Test threat status mapping to OCSF confidence levels."""
        # Test standard mappings (only active and cleared per official API)
        self.assertEqual(
            map_threat_status_to_confidence("active"), Confidence.HIGH
        )
        self.assertEqual(
            map_threat_status_to_confidence("cleared"), Confidence.MEDIUM
        )

        # Test case insensitive mapping
        self.assertEqual(
            map_threat_status_to_confidence("ACTIVE"), Confidence.HIGH
        )
        self.assertEqual(
            map_threat_status_to_confidence("CLEARED"), Confidence.MEDIUM
        )

        # Test unknown status (falsePositive not in official API)
        self.assertEqual(
            map_threat_status_to_confidence("falsePositive"), Confidence.UNKNOWN
        )
        self.assertEqual(
            map_threat_status_to_confidence("unknown"), Confidence.UNKNOWN
        )
        self.assertEqual(
            map_threat_status_to_confidence(""), Confidence.UNKNOWN
        )
        self.assertEqual(
            map_threat_status_to_confidence(None), Confidence.UNKNOWN
        )

    def test_normalize_threat_actors(self):
        """Test threat actor normalization to comment string."""
        # Test single actor with ID (new format includes ID)
        actors = [{"id": "123", "name": "test_actor"}]
        result = normalize_threat_actors(actors)
        self.assertEqual(result, "Associated threat actors: test_actor (ID: 123)")

        # Test multiple actors
        actors = [
            {"id": "123", "name": "actor_one"},
            {"id": "456", "name": "actor_two"},
        ]
        result = normalize_threat_actors(actors)
        self.assertEqual(result, "Associated threat actors: actor_one (ID: 123), actor_two (ID: 456)")

        # Test actor without ID
        actors = [{"name": "test_actor"}]
        result = normalize_threat_actors(actors)
        self.assertEqual(result, "Associated threat actors: test_actor")

        # Test empty actors list
        self.assertIsNone(normalize_threat_actors([]))
        self.assertIsNone(normalize_threat_actors(None))

        # Test actors without names
        actors = [{"id": "123"}]
        self.assertIsNone(normalize_threat_actors(actors))

        # Test invalid actor data
        actors = ["invalid", {"name": "valid_actor"}]
        result = normalize_threat_actors(actors)
        self.assertEqual(result, "Associated threat actors: valid_actor")

    def test_normalize_threat_to_osint(self):
        """Test complete threat data normalization to OCSF OSINT format."""
        # Load test data
        threat_data = load_data("threat_summary")["malware_attachment_threat"]

        # Normalize to OSINT
        osint = normalize_threat_to_osint(threat_data)

        # Verify basic fields (using new API field names)
        self.assertIsInstance(osint, OSINT)
        self.assertEqual(osint.uid, threat_data["id"])
        self.assertEqual(osint.value, threat_data["name"])
        self.assertEqual(osint.type_id, OSINTIndicatorType.FILE.id)
        self.assertEqual(osint.category, "malware")
        self.assertEqual(osint.confidence_id, Confidence.HIGH.id)
        self.assertIsNone(osint.src_url)  # No src_url in official API
        self.assertEqual(osint.vendor_name, "Proofpoint")

        # Verify no campaign (not in official API)
        self.assertIsNone(osint.campaign)

        # Verify comment contains threat details (using new field names)
        self.assertIn("Category: malware", osint.comment)
        self.assertIn("Status: active", osint.comment)
        self.assertIn("Type: attachment", osint.comment)
        self.assertIn("Severity: 850", osint.comment)
        self.assertIn("Associated threat actors: dash_actor_05cbc5", osint.comment)
        self.assertIn("Threat Families: Banking Trojan", osint.comment)
        self.assertIn("Malware: Ursnif", osint.comment)

    def test_normalize_threat_to_osint_minimal_data(self):
        """Test OSINT normalization with minimal threat data."""
        threat_data = load_data("threat_summary")["minimal_threat"]

        osint = normalize_threat_to_osint(threat_data)

        # Verify basic fields with minimal data (using new API structure)
        self.assertEqual(osint.uid, "minimal123")
        self.assertEqual(osint.value, "Minimal Threat Data")  # Uses name field
        self.assertEqual(osint.type_id, OSINTIndicatorType.FILE.id)  # attachment type
        self.assertEqual(osint.category, "malware")
        self.assertEqual(osint.confidence_id, Confidence.HIGH.id)  # active status
        self.assertEqual(osint.vendor_name, "Proofpoint")
        self.assertIsNone(osint.campaign)

    def test_normalize_threat_to_osint_missing_fields(self):
        """Test OSINT normalization with missing fields."""
        threat_data = load_data("threat_summary")["missing_fields_threat"]

        osint = normalize_threat_to_osint(threat_data)

        # Should handle missing fields gracefully (using new API structure)
        self.assertEqual(osint.uid, "missing456")
        self.assertEqual(osint.value, "Threat with Missing Optional Fields")  # Uses name field
        self.assertEqual(osint.type_id, OSINTIndicatorType.URL.id)  # url type
        self.assertEqual(osint.category, "phish")
        self.assertEqual(osint.confidence_id, Confidence.HIGH.id)  # active status

    def test_normalize_threat_to_osint_phishing_url(self):
        """Test OSINT normalization for phishing URL threat."""
        threat_data = load_data("threat_summary")["phishing_url_threat"]

        osint = normalize_threat_to_osint(threat_data)

        # Verify URL-specific mapping (using new API structure)
        self.assertEqual(osint.type_id, OSINTIndicatorType.URL.id)
        self.assertEqual(osint.category, "phish")  # lowercase in new API
        self.assertEqual(osint.confidence_id, Confidence.HIGH.id)

        # Verify multiple actors in comment (with IDs)
        self.assertIn("phishing_group_alpha (ID: 111180bc-82d5-5171-a265-d701775c7850)", osint.comment)
        self.assertIn("credential_harvester_beta (ID: 222290cd-93e6-6282-b376-e812886d8961)", osint.comment)

    def test_normalize_threat_to_osint_cleared_threat(self):
        """Test OSINT normalization for cleared threat."""
        threat_data = load_data("threat_summary")["cleared_threat"]

        osint = normalize_threat_to_osint(threat_data)

        # Verify cleared threat mapping
        self.assertEqual(osint.confidence_id, Confidence.MEDIUM.id)
        self.assertIn("Status: cleared", osint.comment)

    @responses.activate
    def test_get_threat_details_success(self):
        """Test successful threat details retrieval and OSINT normalization."""
        # Load test data
        threat_data = load_data("threat_summary")["malware_attachment_threat"]
        threat_id = threat_data["id"]  # Use new field name

        # Mock the API response
        responses.get(
            f"https://test_url.com/v2/threat/summary/{threat_id}",
            json=threat_data,
            status=200,
        )

        # Execute the action
        result = self.integration.invoke_action(
            IntegrationActionType.GET_THREAT_DETAILS,
            action_args=EmailThreatIdentifierArgs(
                email_threat_id=EmailThreatIdentifier(
                    value_type="proofpoint",
                    value=threat_id
                )
            ),
        )

        # Verify the result
        self.assertIsNotNone(result)
        self.assertIsNotNone(result.result)

        osint = result.result
        self.assertIsInstance(osint, OSINT)
        self.assertEqual(osint.uid, threat_id)
        self.assertEqual(osint.value, "Malicious PDF Document - Banking Trojan")  # Uses name field
        self.assertEqual(osint.type_id, OSINTIndicatorType.FILE.id)
        self.assertEqual(osint.category, "malware")  # lowercase in new API
        self.assertEqual(osint.confidence_id, Confidence.HIGH.id)
        self.assertEqual(osint.vendor_name, "Proofpoint")

    @responses.activate
    def test_get_threat_details_phishing_url(self):
        """Test threat details retrieval for phishing URL."""
        # Load test data
        threat_data = load_data("threat_summary")["phishing_url_threat"]
        threat_id = threat_data["id"]  # Use new field name

        # Mock the API response
        responses.get(
            f"https://test_url.com/v2/threat/summary/{threat_id}",
            json=threat_data,
            status=200,
        )

        # Execute the action
        result = self.integration.invoke_action(
            IntegrationActionType.GET_THREAT_DETAILS,
            action_args=EmailThreatIdentifierArgs(
                email_threat_id=EmailThreatIdentifier(
                    value_type="proofpoint",
                    value=threat_id
                )
            ),
        )

        # Verify URL-specific results
        osint = result.result
        self.assertEqual(osint.type_id, OSINTIndicatorType.URL.id)
        self.assertEqual(osint.category, "phish")  # lowercase in new API
        self.assertEqual(osint.value, "Phishing URL - Credential Harvesting")  # Uses name field

        # Verify multiple actors in comment (with IDs)
        self.assertIn("phishing_group_alpha (ID: 111180bc-82d5-5171-a265-d701775c7850)", osint.comment)
        self.assertIn("credential_harvester_beta (ID: 222290cd-93e6-6282-b376-e812886d8961)", osint.comment)

    @responses.activate
    def test_get_threat_details_spam_email(self):
        """Test threat details retrieval for spam email."""
        # Load test data
        threat_data = load_data("threat_summary")["spam_email_threat"]
        threat_id = threat_data["id"]  # Use new field name

        # Mock the API response
        responses.get(
            f"https://test_url.com/v2/threat/summary/{threat_id}",
            json=threat_data,
            status=200,
        )

        # Execute the action
        result = self.integration.invoke_action(
            IntegrationActionType.GET_THREAT_DETAILS,
            action_args=EmailThreatIdentifierArgs(
                email_threat_id=EmailThreatIdentifier(
                    value_type="proofpoint",
                    value=threat_id
                )
            ),
        )

        # Verify spam-specific results
        osint = result.result
        self.assertEqual(osint.type_id, OSINTIndicatorType.EMAIL.id)
        self.assertEqual(osint.category, "spam")  # lowercase in new API
        self.assertEqual(osint.confidence_id, Confidence.MEDIUM.id)  # cleared status
        self.assertIsNone(osint.campaign)  # No campaign for this threat

    def test_get_threat_details_action_metadata(self):
        """Test that the GetThreatDetails action is properly configured."""
        # Verify the action is available in the integration
        action_types = [action.action_type for action in self.integration.actions]
        self.assertIn(IntegrationActionType.GET_THREAT_DETAILS, action_types)

        # Get the specific action class
        action_class = None
        for action_cls in self.integration.actions:
            if action_cls.action_type == IntegrationActionType.GET_THREAT_DETAILS:
                action_class = action_cls
                break

        self.assertIsNotNone(action_class)
        self.assertEqual(action_class.__name__, "ProofpointV1GetThreatDetails")
        self.assertEqual(action_class.name, "Get threat details")

        # Verify metadata
        self.assertEqual(action_class.metadata.args_type, EmailThreatIdentifierArgs)
        self.assertEqual(action_class.metadata.result_type.__name__, "ThreatDetailsResult")


